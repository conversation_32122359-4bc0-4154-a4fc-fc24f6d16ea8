#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Best Sellers Scraper 运行脚本
快速启动爬虫程序
"""

from amazon_scraper import AmazonScraper
import sys
import os
from datetime import datetime

def main():
    """主运行函数"""
    print("🚀 Amazon Best Sellers Scraper")
    print("=" * 50)
    print("目标类别: Stand-Up Paddleboard Accessories")
    print("爬取数量: 前50名产品")
    print("=" * 50)
    
    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"amazon_bestsellers_paddleboard_{timestamp}.xlsx"
    
    print(f"📁 输出文件: {output_filename}")
    print("\n开始爬取...")
    
    try:
        # 创建爬虫实例并运行
        scraper = AmazonScraper()
        success = scraper.run(output_filename)
        
        if success:
            print(f"\n✅ 爬取成功完成！")
            print(f"📊 Excel文件已保存: {output_filename}")
            
            # 检查文件是否存在
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename)
                print(f"📏 文件大小: {file_size} 字节")
            
            print("\n📋 数据字段说明:")
            print("   • 产品名称 - 完整产品标题")
            print("   • 产品图片URL - 主图片链接")
            print("   • 产品原价 - 原始价格")
            print("   • 产品折扣价 - 当前售价 (加粗显示)")
            print("   • 产品评星 - 用户评分")
            print("   • 评论数 - 评论总数")
            print("   • ASIN码 - 亚马逊产品标识")
            print("   • 产品链接 - 完整产品页面URL")
            
        else:
            print("\n❌ 爬取失败，请检查网络连接和日志信息")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了爬取过程")
        return 1
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

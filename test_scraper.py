#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Scraper 测试脚本
用于测试爬虫的各个功能模块
"""

import unittest
from amazon_scraper import AmazonScraper
import os
import pandas as pd
from unittest.mock import patch, MagicMock

class TestAmazonScraper(unittest.TestCase):
    """Amazon爬虫测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.scraper = AmazonScraper()
    
    def test_extract_asin_from_url(self):
        """测试ASIN提取功能"""
        test_urls = [
            "https://www.amazon.com/dp/B08N5WRWNW/ref=sr_1_1",
            "https://www.amazon.com/product/dp/B07QXMNF1X",
            "https://www.amazon.com/invalid-url"
        ]
        
        expected_results = ["B08N5WRWNW", "B07QXMNF1X", "N/A"]
        
        for url, expected in zip(test_urls, expected_results):
            with self.subTest(url=url):
                result = self.scraper.extract_asin_from_url(url)
                self.assertEqual(result, expected)
    
    def test_clean_price(self):
        """测试价格清理功能"""
        test_prices = [
            "$29.99",
            "29.99",
            "$29.99 - $39.99",
            "Price: $29.99",
            "",
            None
        ]
        
        expected_results = ["$29.99", "$29.99", "$29.99", "$29.99", "N/A", "N/A"]
        
        for price, expected in zip(test_prices, expected_results):
            with self.subTest(price=price):
                result = self.scraper.clean_price(price)
                self.assertEqual(result, expected)
    
    def test_extract_rating(self):
        """测试评星提取功能"""
        test_ratings = [
            "4.5 out of 5 stars",
            "4.2 stars",
            "5.0 out of 5",
            "",
            None
        ]
        
        expected_results = ["4.5/5", "4.2/5", "5.0/5", "N/A", "N/A"]
        
        for rating, expected in zip(test_ratings, expected_results):
            with self.subTest(rating=rating):
                result = self.scraper.extract_rating(rating)
                self.assertEqual(result, expected)
    
    def test_random_delay(self):
        """测试随机延迟功能"""
        import time
        start_time = time.time()
        self.scraper.random_delay(0.1, 0.2)
        end_time = time.time()
        
        delay = end_time - start_time
        self.assertGreaterEqual(delay, 0.1)
        self.assertLessEqual(delay, 0.3)  # 允许一些误差
    
    def test_product_data_structure(self):
        """测试产品数据结构"""
        expected_fields = [
            '产品名称', '产品图片URL', '产品原价', '产品折扣价',
            '产品评星', '评论数', 'ASIN码', '产品链接'
        ]
        
        # 模拟产品数据
        test_url = "https://www.amazon.com/dp/B08N5WRWNW"
        product_data = {
            '产品名称': 'Test Product',
            '产品图片URL': 'https://example.com/image.jpg',
            '产品原价': '$29.99',
            '产品折扣价': '$24.99',
            '产品评星': '4.5/5',
            '评论数': '100',
            'ASIN码': self.scraper.extract_asin_from_url(test_url),
            '产品链接': test_url
        }
        
        # 检查所有必需字段是否存在
        for field in expected_fields:
            self.assertIn(field, product_data)
        
        # 检查ASIN提取是否正确
        self.assertEqual(product_data['ASIN码'], 'B08N5WRWNW')


class TestScraperIntegration(unittest.TestCase):
    """集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.scraper = AmazonScraper()
        self.test_filename = "test_output.xlsx"
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.test_filename):
            os.remove(self.test_filename)
    
    def test_excel_generation_with_mock_data(self):
        """测试Excel生成功能（使用模拟数据）"""
        # 创建模拟数据
        mock_data = [
            {
                '产品名称': 'Test Product 1',
                '产品图片URL': 'https://example.com/image1.jpg',
                '产品原价': '$29.99',
                '产品折扣价': '$24.99',
                '产品评星': '4.5/5',
                '评论数': '100',
                'ASIN码': 'B08N5WRWNW',
                '产品链接': 'https://www.amazon.com/dp/B08N5WRWNW'
            },
            {
                '产品名称': 'Test Product 2',
                '产品图片URL': 'https://example.com/image2.jpg',
                '产品原价': '$39.99',
                '产品折扣价': '$34.99',
                '产品评星': '4.2/5',
                '评论数': '50',
                'ASIN码': 'B07QXMNF1X',
                '产品链接': 'https://www.amazon.com/dp/B07QXMNF1X'
            }
        ]
        
        # 设置模拟数据
        self.scraper.products_data = mock_data
        
        # 生成Excel文件
        self.scraper.generate_excel_report(self.test_filename)
        
        # 验证文件是否生成
        self.assertTrue(os.path.exists(self.test_filename))
        
        # 验证Excel内容
        df = pd.read_excel(self.test_filename, sheet_name='产品数据')
        self.assertEqual(len(df), 2)
        self.assertEqual(list(df.columns), [
            '产品名称', '产品图片URL', '产品原价', '产品折扣价',
            '产品评星', '评论数', 'ASIN码', '产品链接'
        ])


def run_functionality_test():
    """运行功能测试"""
    print("🧪 Amazon Scraper 功能测试")
    print("=" * 40)
    
    # 创建爬虫实例
    scraper = AmazonScraper()
    
    # 测试基本功能
    print("1. 测试ASIN提取...")
    test_url = "https://www.amazon.com/dp/B08N5WRWNW/ref=sr_1_1"
    asin = scraper.extract_asin_from_url(test_url)
    print(f"   URL: {test_url}")
    print(f"   ASIN: {asin}")
    assert asin == "B08N5WRWNW", "ASIN提取失败"
    print("   ✅ ASIN提取测试通过")
    
    print("\n2. 测试价格清理...")
    test_price = "$29.99"
    cleaned_price = scraper.clean_price(test_price)
    print(f"   原价格: {test_price}")
    print(f"   清理后: {cleaned_price}")
    assert cleaned_price == "$29.99", "价格清理失败"
    print("   ✅ 价格清理测试通过")
    
    print("\n3. 测试评星提取...")
    test_rating = "4.5 out of 5 stars"
    rating = scraper.extract_rating(test_rating)
    print(f"   原评星: {test_rating}")
    print(f"   提取后: {rating}")
    assert rating == "4.5/5", "评星提取失败"
    print("   ✅ 评星提取测试通过")
    
    print("\n4. 测试Excel生成...")
    # 创建测试数据
    test_data = [{
        '产品名称': 'Test Product',
        '产品图片URL': 'https://example.com/image.jpg',
        '产品原价': '$29.99',
        '产品折扣价': '$24.99',
        '产品评星': '4.5/5',
        '评论数': '100',
        'ASIN码': 'B08N5WRWNW',
        '产品链接': 'https://www.amazon.com/dp/B08N5WRWNW'
    }]
    
    scraper.products_data = test_data
    test_filename = "functionality_test.xlsx"
    
    try:
        scraper.generate_excel_report(test_filename)
        if os.path.exists(test_filename):
            print(f"   ✅ Excel文件生成成功: {test_filename}")
            os.remove(test_filename)  # 清理测试文件
        else:
            print("   ❌ Excel文件生成失败")
    except Exception as e:
        print(f"   ❌ Excel生成出错: {str(e)}")
    
    print("\n🎉 所有功能测试完成！")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "unittest":
        # 运行单元测试
        unittest.main(argv=[''], exit=False)
    else:
        # 运行功能测试
        run_functionality_test()

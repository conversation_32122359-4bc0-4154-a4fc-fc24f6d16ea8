# Amazon Scraper Windows 运行指南

## 🚨 问题解决方案

### 错误原因
您遇到的错误是因为代码编辑器试图使用Linux路径格式 `/usr/bin/env python3` 在Windows系统上运行Python，导致"系统找不到指定的路径"。

## ✅ 正确的运行方法

### 方法1: 命令行运行（推荐）

1. **打开命令提示符**
   - 按 `Win + R`
   - 输入 `cmd` 并回车
   - 或者按 `Win + X` 选择"命令提示符"

2. **切换到项目目录**
   ```cmd
   cd "d:\LSK\02 文件合集\99 personal\01-augment\AmazonSpider"
   ```

3. **检查Python环境**
   ```cmd
   python --version
   ```
   或者
   ```cmd
   python3 --version
   ```

4. **安装依赖包**
   ```cmd
   pip install requests beautifulsoup4 selenium pandas openpyxl lxml fake-useragent webdriver-manager
   ```

5. **运行爬虫**
   ```cmd
   python amazon_scraper.py
   ```
   或者
   ```cmd
   python3 amazon_scraper.py
   ```

### 方法2: 使用PowerShell

1. **打开PowerShell**
   - 按 `Win + X` 选择"Windows PowerShell"

2. **运行命令**
   ```powershell
   cd "d:\LSK\02 文件合集\99 personal\01-augment\AmazonSpider"
   python amazon_scraper.py
   ```

### 方法3: 使用批处理脚本

直接双击运行 `setup_and_run.bat` 文件，它会自动：
- 检查Python环境
- 安装依赖包
- 运行功能测试
- 启动爬虫程序

## 🔧 依赖包安装验证

运行以下命令检查依赖包是否正确安装：

```cmd
python -c "import requests, bs4, selenium, pandas, openpyxl; print('所有依赖包已安装')"
```

如果出现错误，请逐个安装：

```cmd
pip install requests
pip install beautifulsoup4
pip install selenium
pip install pandas
pip install openpyxl
pip install lxml
pip install fake-useragent
pip install webdriver-manager
```

## 🌐 Chrome浏览器要求

完整版本需要Chrome浏览器：
1. 确保已安装Chrome浏览器
2. 程序会自动下载和管理ChromeDriver
3. 如果遇到驱动问题，运行：
   ```cmd
   pip install --upgrade webdriver-manager
   ```

## 🚀 推荐运行流程

1. **先运行演示版本确认环境**
   ```cmd
   python simple_demo.py
   ```

2. **如果演示版本成功，再运行完整版本**
   ```cmd
   python amazon_scraper.py
   ```

3. **或使用运行脚本**
   ```cmd
   python run_scraper.py
   ```

## ⚠️ 常见问题解决

### 问题1: "python不是内部或外部命令"
**解决方案**: Python未添加到系统PATH
- 重新安装Python，勾选"Add Python to PATH"
- 或手动添加Python路径到系统环境变量

### 问题2: 依赖包安装失败
**解决方案**: 
```cmd
python -m pip install --upgrade pip
pip install -r requirements.txt
```

### 问题3: Chrome驱动问题
**解决方案**:
```cmd
pip install --upgrade webdriver-manager
```

### 问题4: 网络连接问题
**解决方案**:
- 检查网络连接
- 考虑使用VPN
- 增加延迟时间

## 📝 运行日志示例

成功运行时应该看到类似输出：
```
🚀 Amazon Best Sellers Scraper 开始运行
目标: Stand-Up Paddleboard Accessories 前50名产品
============================================================
2025-07-08 15:00:00,000 - INFO - 开始提取产品链接...
2025-07-08 15:00:05,000 - INFO - 成功提取到 50 个产品链接
2025-07-08 15:00:05,000 - INFO - 正在处理第 1/50 个产品
...
```

## 🎯 成功标志

运行成功后会生成：
- Excel文件：`amazon_bestsellers_paddleboard_YYYYMMDD_HHMMSS.xlsx`
- 包含50个产品的完整数据
- 专业格式化的表格样式

## 📞 技术支持

如果仍有问题，请提供：
1. Python版本 (`python --version`)
2. 操作系统版本
3. 完整的错误信息
4. 运行的具体命令

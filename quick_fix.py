# -*- coding: utf-8 -*-
"""
快速修复脚本 - 解决Windows运行问题
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print(f"✅ {description}成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description}失败")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description}超时")
        return False
    except Exception as e:
        print(f"❌ {description}出错: {e}")
        return False

def main():
    print("🛠️ Amazon Scraper 快速修复工具")
    print("=" * 50)
    
    # 1. 检查Python版本
    print("\n1️⃣ 检查Python环境")
    run_command("python --version", "检查Python版本")
    
    # 2. 升级pip
    print("\n2️⃣ 升级pip")
    run_command("python -m pip install --upgrade pip", "升级pip")
    
    # 3. 安装基础依赖
    print("\n3️⃣ 安装基础依赖包")
    basic_packages = ["requests", "pandas", "openpyxl"]
    for package in basic_packages:
        run_command(f"pip install {package}", f"安装{package}")
    
    # 4. 测试基础功能
    print("\n4️⃣ 测试基础功能")
    try:
        import requests
        import pandas
        import openpyxl
        print("✅ 基础包导入成功")
        
        # 运行演示版本
        print("\n5️⃣ 运行演示版本")
        if run_command("python simple_demo.py", "运行演示版本"):
            print("\n🎉 演示版本运行成功！")
            print("📁 请查看生成的Excel文件")
            
            # 询问是否安装完整版本依赖
            print("\n6️⃣ 完整版本依赖安装（可选）")
            choice = input("\n是否安装完整版本依赖？这需要更多时间和网络流量 (y/N): ")
            
            if choice.lower() in ['y', 'yes', '是']:
                print("\n正在安装完整版本依赖...")
                full_packages = ["beautifulsoup4", "selenium", "lxml", "fake-useragent", "webdriver-manager"]
                
                success_count = 0
                for package in full_packages:
                    if run_command(f"pip install {package}", f"安装{package}"):
                        success_count += 1
                
                if success_count == len(full_packages):
                    print("\n✅ 完整版本依赖安装成功！")
                    print("现在可以运行: python amazon_scraper.py")
                else:
                    print(f"\n⚠️ 部分依赖安装失败 ({success_count}/{len(full_packages)})")
                    print("建议先使用演示版本")
            else:
                print("\n💡 您可以继续使用演示版本，或稍后手动安装完整版本依赖")
        else:
            print("\n❌ 演示版本运行失败")
            
    except ImportError as e:
        print(f"❌ 基础包导入失败: {e}")
        print("请检查Python环境和网络连接")
    
    print("\n" + "=" * 50)
    print("🔧 修复完成")
    print("\n📋 使用说明:")
    print("• 演示版本: python simple_demo.py")
    print("• 完整版本: python amazon_scraper.py (需要完整依赖)")
    print("• Windows启动器: python windows_run.py")
    print("• 批处理脚本: setup_and_run.bat")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()

# -*- coding: utf-8 -*-
"""
Windows专用启动脚本
自动检查和安装依赖，然后运行Amazon爬虫
"""

import subprocess
import sys
import os
from pathlib import Path

def check_python():
    """检查Python环境"""
    print("🔍 检查Python环境...")
    try:
        version = sys.version_info
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ 需要Python 3.8或更高版本")
            return False
        return True
    except Exception as e:
        print(f"❌ Python检查失败: {e}")
        return False

def install_package(package):
    """安装单个包"""
    try:
        print(f"📦 安装 {package}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✅ {package} 安装成功")
            return True
        else:
            print(f"❌ {package} 安装失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {package} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package} 安装出错: {e}")
        return False

def check_and_install_dependencies():
    """检查并安装依赖包"""
    print("\n📦 检查和安装依赖包...")
    
    # 基础依赖包（演示版本需要）
    basic_packages = [
        "requests",
        "pandas", 
        "openpyxl"
    ]
    
    # 完整版本依赖包
    full_packages = [
        "beautifulsoup4",
        "selenium",
        "lxml", 
        "fake-useragent",
        "webdriver-manager"
    ]
    
    # 先安装基础包
    print("\n🔧 安装基础依赖包...")
    basic_success = True
    for package in basic_packages:
        if not install_package(package):
            basic_success = False
    
    if not basic_success:
        print("❌ 基础依赖包安装失败，只能运行演示版本")
        return False
    
    # 再安装完整版本包
    print("\n🔧 安装完整版本依赖包...")
    full_success = True
    for package in full_packages:
        if not install_package(package):
            full_success = False
    
    return full_success

def test_imports():
    """测试导入"""
    print("\n🧪 测试依赖包导入...")
    
    # 测试基础包
    try:
        import requests
        import pandas
        import openpyxl
        print("✅ 基础包导入成功")
        basic_ok = True
    except ImportError as e:
        print(f"❌ 基础包导入失败: {e}")
        basic_ok = False
    
    # 测试完整版本包
    try:
        import bs4
        import selenium
        import fake_useragent
        from webdriver_manager.chrome import ChromeDriverManager
        print("✅ 完整版本包导入成功")
        full_ok = True
    except ImportError as e:
        print(f"⚠️ 完整版本包导入失败: {e}")
        full_ok = False
    
    return basic_ok, full_ok

def run_demo():
    """运行演示版本"""
    print("\n🚀 运行演示版本...")
    try:
        result = subprocess.run([sys.executable, "simple_demo.py"], 
                              cwd=Path(__file__).parent, timeout=60)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("⏰ 演示版本运行超时")
        return False
    except Exception as e:
        print(f"❌ 演示版本运行失败: {e}")
        return False

def run_full_scraper():
    """运行完整版本爬虫"""
    print("\n🚀 运行完整版本爬虫...")
    try:
        result = subprocess.run([sys.executable, "amazon_scraper.py"], 
                              cwd=Path(__file__).parent, timeout=1800)  # 30分钟超时
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("⏰ 完整版本运行超时（30分钟）")
        return False
    except Exception as e:
        print(f"❌ 完整版本运行失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 Amazon Best Sellers Scraper - Windows启动器")
    print("=" * 60)
    
    # 检查Python环境
    if not check_python():
        input("按回车键退出...")
        return
    
    # 检查并安装依赖
    full_deps_ok = check_and_install_dependencies()
    
    # 测试导入
    basic_ok, full_ok = test_imports()
    
    if not basic_ok:
        print("\n❌ 基础依赖包有问题，无法运行任何版本")
        input("按回车键退出...")
        return
    
    print("\n" + "=" * 60)
    
    if full_ok:
        print("✅ 所有依赖包就绪，可以运行完整版本")
        choice = input("\n选择运行模式:\n1. 演示版本（快速，使用模拟数据）\n2. 完整版本（真实爬取亚马逊数据）\n请输入选择 (1/2): ")
        
        if choice == "1":
            success = run_demo()
        elif choice == "2":
            print("\n⚠️ 注意：完整版本需要Chrome浏览器，运行时间约10-15分钟")
            confirm = input("确认运行完整版本？(y/N): ")
            if confirm.lower() in ['y', 'yes', '是']:
                success = run_full_scraper()
            else:
                print("已取消运行")
                success = False
        else:
            print("无效选择，运行演示版本")
            success = run_demo()
    else:
        print("⚠️ 完整版本依赖包有问题，只能运行演示版本")
        success = run_demo()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 运行成功完成！")
        print("📁 请查看生成的Excel文件")
    else:
        print("❌ 运行失败，请检查错误信息")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()

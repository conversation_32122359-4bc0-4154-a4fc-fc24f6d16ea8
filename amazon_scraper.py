#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Best Sellers Scraper
专门爬取亚马逊Stand-Up Paddleboard Accessories类别的前50名畅销产品
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import re
from fake_useragent import UserAgent
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import logging
from typing import List, Dict, Optional
import json

class AmazonScraper:
    """亚马逊畅销产品爬虫类"""
    
    def __init__(self):
        self.base_url = "https://www.amazon.com/Best-Sellers-Sports-Outdoors-Stand-Up-Paddleboard-Accessories/zgbs/sporting-goods/10208156011/ref=zg_bs_nav_sporting-goods_4_3204434011"
        self.session = requests.Session()
        self.ua = UserAgent()
        self.products_data = []
        self.error_log = []
        
        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        # 设置请求头
        self.headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)
    
    def setup_selenium_driver(self) -> webdriver.Chrome:
        """设置Selenium WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument(f'--user-agent={self.ua.random}')
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        return driver
    
    def random_delay(self, min_delay: float = 1.0, max_delay: float = 3.0):
        """随机延迟，避免被检测"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def extract_product_links(self) -> List[str]:
        """提取产品链接列表"""
        self.logger.info("开始提取产品链接...")
        
        driver = self.setup_selenium_driver()
        product_links = []
        
        try:
            driver.get(self.base_url)
            self.random_delay(2, 4)
            
            # 等待页面加载
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='best-sellers-list']"))
            )
            
            # 查找产品链接
            product_elements = driver.find_elements(By.CSS_SELECTOR, "a[href*='/dp/']")
            
            for element in product_elements[:50]:  # 只取前50个
                href = element.get_attribute('href')
                if href and '/dp/' in href:
                    # 清理URL，只保留产品页面部分
                    clean_url = href.split('?')[0] if '?' in href else href
                    if clean_url not in product_links:
                        product_links.append(clean_url)
            
            self.logger.info(f"成功提取到 {len(product_links)} 个产品链接")
            
        except Exception as e:
            self.logger.error(f"提取产品链接时出错: {str(e)}")
            self.error_log.append(f"提取产品链接失败: {str(e)}")
        
        finally:
            driver.quit()
        
        return product_links[:50]  # 确保只返回前50个
    
    def extract_asin_from_url(self, url: str) -> str:
        """从URL中提取ASIN码"""
        match = re.search(r'/dp/([A-Z0-9]{10})', url)
        return match.group(1) if match else "N/A"
    
    def clean_price(self, price_text: str) -> str:
        """清理价格文本"""
        if not price_text:
            return "N/A"
        
        # 移除多余的空白字符和符号
        price = re.sub(r'[^\d.,\$]', '', price_text.strip())
        if price.startswith('$'):
            return price
        elif price:
            return f"${price}"
        return "N/A"
    
    def extract_rating(self, rating_text: str) -> str:
        """提取评星信息"""
        if not rating_text:
            return "N/A"
        
        # 查找数字评分
        match = re.search(r'(\d+\.?\d*)\s*out\s*of\s*5', rating_text)
        if match:
            return f"{match.group(1)}/5"
        
        # 查找星级
        match = re.search(r'(\d+\.?\d*)\s*stars?', rating_text)
        if match:
            return f"{match.group(1)}/5"
        
        return rating_text.strip() if rating_text.strip() else "N/A"

    def extract_product_details(self, product_url: str) -> Dict:
        """提取单个产品的详细信息"""
        self.logger.info(f"正在提取产品详情: {product_url}")

        product_data = {
            '产品名称': 'N/A',
            '产品图片URL': 'N/A',
            '产品原价': 'N/A',
            '产品折扣价': 'N/A',
            '产品评星': 'N/A',
            '评论数': 'N/A',
            'ASIN码': self.extract_asin_from_url(product_url),
            '产品链接': product_url
        }

        driver = self.setup_selenium_driver()

        try:
            driver.get(product_url)
            self.random_delay(2, 4)

            # 等待页面加载
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "productTitle"))
            )

            # 提取产品名称
            try:
                title_element = driver.find_element(By.ID, "productTitle")
                product_data['产品名称'] = title_element.text.strip()
            except:
                try:
                    title_element = driver.find_element(By.CSS_SELECTOR, "h1 span")
                    product_data['产品名称'] = title_element.text.strip()
                except:
                    self.logger.warning(f"无法提取产品名称: {product_url}")

            # 提取产品图片URL
            try:
                img_element = driver.find_element(By.CSS_SELECTOR, "#landingImage, #imgTagWrapperId img")
                product_data['产品图片URL'] = img_element.get_attribute('src')
            except:
                try:
                    img_element = driver.find_element(By.CSS_SELECTOR, "img[data-old-hires]")
                    product_data['产品图片URL'] = img_element.get_attribute('data-old-hires')
                except:
                    self.logger.warning(f"无法提取产品图片: {product_url}")

            # 提取价格信息
            try:
                # 尝试提取折扣价
                discount_price = None
                try:
                    discount_element = driver.find_element(By.CSS_SELECTOR, ".a-price-whole")
                    discount_price = discount_element.text.strip()
                    if discount_price:
                        product_data['产品折扣价'] = self.clean_price(f"${discount_price}")
                except:
                    pass

                # 尝试提取原价
                try:
                    original_price_element = driver.find_element(By.CSS_SELECTOR, ".a-text-price .a-offscreen")
                    original_price = original_price_element.get_attribute('textContent')
                    if original_price:
                        product_data['产品原价'] = self.clean_price(original_price)
                except:
                    pass

                # 如果没有找到折扣价，使用当前价格作为折扣价
                if product_data['产品折扣价'] == 'N/A':
                    try:
                        current_price_element = driver.find_element(By.CSS_SELECTOR, ".a-price .a-offscreen")
                        current_price = current_price_element.get_attribute('textContent')
                        if current_price:
                            product_data['产品折扣价'] = self.clean_price(current_price)
                    except:
                        pass

                # 如果没有原价，使用折扣价作为原价
                if product_data['产品原价'] == 'N/A' and product_data['产品折扣价'] != 'N/A':
                    product_data['产品原价'] = product_data['产品折扣价']

            except Exception as e:
                self.logger.warning(f"提取价格信息时出错: {str(e)}")

            # 提取评星信息
            try:
                rating_element = driver.find_element(By.CSS_SELECTOR, "[data-hook='average-star-rating'] span")
                rating_text = rating_element.get_attribute('textContent')
                product_data['产品评星'] = self.extract_rating(rating_text)
            except:
                try:
                    rating_element = driver.find_element(By.CSS_SELECTOR, ".a-icon-alt")
                    rating_text = rating_element.get_attribute('textContent')
                    product_data['产品评星'] = self.extract_rating(rating_text)
                except:
                    self.logger.warning(f"无法提取评星信息: {product_url}")

            # 提取评论数
            try:
                review_element = driver.find_element(By.CSS_SELECTOR, "[data-hook='total-review-count']")
                review_count = review_element.text.strip()
                # 清理评论数文本
                review_count = re.sub(r'[^\d,]', '', review_count)
                product_data['评论数'] = review_count if review_count else "N/A"
            except:
                try:
                    review_element = driver.find_element(By.CSS_SELECTOR, "#acrCustomerReviewText")
                    review_text = review_element.text.strip()
                    match = re.search(r'(\d+(?:,\d+)*)', review_text)
                    product_data['评论数'] = match.group(1) if match else "N/A"
                except:
                    self.logger.warning(f"无法提取评论数: {product_url}")

            self.logger.info(f"成功提取产品信息: {product_data['产品名称']}")

        except Exception as e:
            self.logger.error(f"提取产品详情时出错: {str(e)}")
            self.error_log.append(f"提取产品详情失败 {product_url}: {str(e)}")

        finally:
            driver.quit()

        return product_data

    def scrape_all_products(self) -> List[Dict]:
        """爬取所有产品信息"""
        self.logger.info("开始爬取亚马逊Stand-Up Paddleboard Accessories畅销产品...")

        # 第一步：获取产品链接
        product_links = self.extract_product_links()

        if not product_links:
            self.logger.error("未能获取到任何产品链接")
            return []

        self.logger.info(f"准备爬取 {len(product_links)} 个产品的详细信息")

        # 第二步：逐个爬取产品详情
        for i, product_url in enumerate(product_links, 1):
            self.logger.info(f"正在处理第 {i}/{len(product_links)} 个产品")

            try:
                product_data = self.extract_product_details(product_url)
                self.products_data.append(product_data)

                # 随机延迟，避免被检测
                self.random_delay(1.5, 3.0)

            except Exception as e:
                self.logger.error(f"处理产品 {product_url} 时出错: {str(e)}")
                self.error_log.append(f"处理产品失败 {product_url}: {str(e)}")
                continue

        self.logger.info(f"爬取完成，共获取 {len(self.products_data)} 个产品信息")
        return self.products_data

    def generate_excel_report(self, filename: str = "amazon_bestsellers_paddleboard_accessories.xlsx"):
        """生成Excel报告"""
        self.logger.info(f"正在生成Excel报告: {filename}")

        if not self.products_data:
            self.logger.warning("没有产品数据可以导出")
            return

        try:
            # 创建DataFrame
            df = pd.DataFrame(self.products_data)

            # 确保列顺序正确
            column_order = ['产品名称', '产品图片URL', '产品原价', '产品折扣价', '产品评星', '评论数', 'ASIN码', '产品链接']
            df = df.reindex(columns=column_order)

            # 创建Excel写入器
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 写入主数据表
                df.to_excel(writer, sheet_name='产品数据', index=False)

                # 获取工作簿和工作表
                workbook = writer.book
                worksheet = writer.sheets['产品数据']

                # 设置样式
                from openpyxl.styles import Font, PatternFill, Alignment

                # 标题行样式
                header_font = Font(bold=True, size=12)
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

                # 应用标题行样式
                for cell in worksheet[1]:
                    cell.font = Font(bold=True, size=12, color="FFFFFF")
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal="center", vertical="center")

                # 折扣价列加粗
                discount_col = column_order.index('产品折扣价') + 1
                for row in range(2, len(df) + 2):
                    cell = worksheet.cell(row=row, column=discount_col)
                    cell.font = Font(bold=True)

                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter

                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                    worksheet.column_dimensions[column_letter].width = adjusted_width

                # 如果有错误日志，创建错误日志表
                if self.error_log:
                    error_df = pd.DataFrame({'错误信息': self.error_log})
                    error_df.to_excel(writer, sheet_name='错误日志', index=False)

                    # 设置错误日志表样式
                    error_worksheet = writer.sheets['错误日志']
                    for cell in error_worksheet[1]:
                        cell.font = Font(bold=True, size=12, color="FFFFFF")
                        cell.fill = PatternFill(start_color="DC143C", end_color="DC143C", fill_type="solid")
                        cell.alignment = Alignment(horizontal="center", vertical="center")

                    # 调整错误日志列宽
                    error_worksheet.column_dimensions['A'].width = 80

            self.logger.info(f"Excel报告生成成功: {filename}")
            self.logger.info(f"共包含 {len(df)} 个产品数据")
            if self.error_log:
                self.logger.info(f"错误日志包含 {len(self.error_log)} 条记录")

        except Exception as e:
            self.logger.error(f"生成Excel报告时出错: {str(e)}")
            raise

    def run(self, output_filename: str = "amazon_bestsellers_paddleboard_accessories.xlsx"):
        """运行完整的爬取流程"""
        self.logger.info("=" * 60)
        self.logger.info("Amazon Best Sellers Scraper 开始运行")
        self.logger.info("目标: Stand-Up Paddleboard Accessories 前50名产品")
        self.logger.info("=" * 60)

        try:
            # 爬取所有产品数据
            products = self.scrape_all_products()

            if products:
                # 生成Excel报告
                self.generate_excel_report(output_filename)

                self.logger.info("=" * 60)
                self.logger.info("爬取任务完成！")
                self.logger.info(f"成功爬取 {len(products)} 个产品")
                self.logger.info(f"Excel文件已保存: {output_filename}")
                self.logger.info("=" * 60)

                return True
            else:
                self.logger.error("未能获取到任何产品数据")
                return False

        except Exception as e:
            self.logger.error(f"爬取过程中发生错误: {str(e)}")
            return False


def main():
    """主函数"""
    scraper = AmazonScraper()
    success = scraper.run()

    if success:
        print("\n✅ 爬取任务成功完成！")
        print("📊 Excel文件已生成，包含以下字段：")
        print("   - 产品名称")
        print("   - 产品图片URL")
        print("   - 产品原价")
        print("   - 产品折扣价 (加粗显示)")
        print("   - 产品评星")
        print("   - 评论数")
        print("   - ASIN码")
        print("   - 产品链接")
    else:
        print("\n❌ 爬取任务失败，请检查日志信息")


if __name__ == "__main__":
    main()

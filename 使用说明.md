# Amazon Best Sellers Scraper 使用说明

## 项目概述

这是一个专门用于爬取亚马逊美国站 "Stand-Up Paddleboard Accessories" 类别前50名畅销产品的数据爬虫工具。

## 功能特点

✅ **精准定位**: 专门针对Stand-Up Paddleboard Accessories类别  
✅ **全面数据**: 提取8个关键字段的完整产品信息  
✅ **Excel输出**: 生成格式化的Excel表格，支持样式和错误日志  
✅ **反爬虫机制**: 内置随机延迟和User-Agent轮换  
✅ **错误处理**: 完善的异常处理和错误日志记录  

## 提取字段

| 字段名 | 描述 | 备注 |
|--------|------|------|
| 产品名称 | 完整的产品标题 | - |
| 产品图片URL | 主图片链接地址 | 高清图片URL |
| 产品原价 | 原始标价 | 美元格式 |
| 产品折扣价 | 当前售价 | **加粗显示** |
| 产品评星 | 用户评分 | X/5格式 |
| 评论数 | 评论总数 | 数字格式 |
| ASIN码 | 亚马逊产品标识 | 10位字符 |
| 产品链接 | 完整产品页面URL | 可直接访问 |

## 快速开始

### 方法1: 运行演示版本（推荐新手）

```bash
python simple_demo.py
```

**特点:**
- 无需安装复杂依赖
- 使用模拟数据展示功能
- 快速了解输出格式
- 生成真实的Excel文件

### 方法2: 运行完整版本

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **运行爬虫**
   ```bash
   python run_scraper.py
   ```

3. **或使用批处理脚本**
   ```bash
   setup_and_run.bat
   ```

## 文件说明

### 核心文件
- `amazon_scraper.py` - 主爬虫程序
- `simple_demo.py` - 演示版本（推荐先运行）
- `run_scraper.py` - 运行脚本
- `requirements.txt` - 依赖包列表

### 辅助文件
- `test_scraper.py` - 测试脚本
- `setup_and_run.bat` - Windows批处理脚本
- `README.md` - 英文说明文档
- `使用说明.md` - 中文使用说明

## 输出文件

### Excel文件格式
- **文件名**: `amazon_bestsellers_paddleboard_YYYYMMDD_HHMMSS.xlsx`
- **主数据表**: 包含所有产品信息，格式化样式
- **错误日志表**: 记录爬取过程中的错误信息（如有）

### 样式特点
- 标题行：蓝色背景，白色字体，加粗
- 折扣价列：加粗显示，突出重点
- 自动调整列宽，最大宽度50字符
- 专业的表格格式

## 技术特性

### 反爬虫策略
- 随机User-Agent轮换
- 智能请求延迟（1-3秒）
- Selenium无头浏览器
- 请求头伪装

### 数据验证
- 字段完整性检查
- 缺失值标记为"N/A"
- 价格格式标准化
- URL有效性验证

### 错误处理
- 网络超时重试
- 元素定位失败处理
- 完整错误日志记录
- 优雅的异常恢复

## 运行环境要求

### 基本要求
- Python 3.8+
- Windows/Linux/macOS
- 稳定的网络连接

### 依赖包
```
requests==2.31.0
beautifulsoup4==4.12.2
selenium==4.15.2
pandas==2.1.3
openpyxl==3.1.2
lxml==4.9.3
fake-useragent==1.4.0
webdriver-manager==4.0.1
```

### Chrome浏览器
- 完整版本需要Chrome浏览器
- 自动下载和管理ChromeDriver
- 支持无头模式运行

## 使用建议

### 首次使用
1. 先运行 `python simple_demo.py` 了解功能
2. 查看生成的Excel文件格式
3. 确认满足需求后再安装完整依赖

### 生产使用
1. 确保网络连接稳定
2. 建议使用VPN（如果网络受限）
3. 运行时间约10-15分钟（50个产品）
4. 避免频繁运行，遵守网站使用条款

### 故障排除
1. **Chrome驱动问题**: 更新webdriver-manager
2. **网络连接超时**: 检查网络或使用VPN
3. **元素定位失败**: 亚马逊页面结构可能变化

## 注意事项

⚠️ **合规使用**
- 遵守亚马逊robots.txt和使用条款
- 不要过于频繁地访问
- 仅用于学习和研究目的

⚠️ **数据准确性**
- 数据来源于公开页面
- 价格和库存可能实时变化
- 建议定期更新数据

⚠️ **技术限制**
- 依赖网页结构稳定性
- 可能受反爬虫机制影响
- 需要适当的访问频率控制

## 联系支持

如果遇到问题或需要定制功能，请：
1. 查看错误日志文件
2. 检查网络连接状态
3. 确认依赖包版本正确
4. 参考故障排除指南

---

**版本**: 1.0.0  
**更新日期**: 2025-07-08  
**兼容性**: Python 3.8+, Windows/Linux/macOS

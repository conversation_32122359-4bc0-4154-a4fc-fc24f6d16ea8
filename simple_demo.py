#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon Best Sellers Scraper - 简化演示版本
不依赖Selenium，使用requests和BeautifulSoup进行演示
"""

import requests
import pandas as pd
import time
import random
import re
from datetime import datetime
import logging

class SimpleAmazonDemo:
    """简化版亚马逊爬虫演示类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.products_data = []
        self.error_log = []
        
        # 设置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)
    
    def extract_asin_from_url(self, url: str) -> str:
        """从URL中提取ASIN码"""
        match = re.search(r'/dp/([A-Z0-9]{10})', url)
        return match.group(1) if match else "N/A"
    
    def clean_price(self, price_text: str) -> str:
        """清理价格文本"""
        if not price_text:
            return "N/A"
        
        # 移除多余的空白字符和符号
        price = re.sub(r'[^\d.,\$]', '', price_text.strip())
        if price.startswith('$'):
            return price
        elif price:
            return f"${price}"
        return "N/A"
    
    def extract_rating(self, rating_text: str) -> str:
        """提取评星信息"""
        if not rating_text:
            return "N/A"
        
        # 查找数字评分
        match = re.search(r'(\d+\.?\d*)\s*out\s*of\s*5', rating_text)
        if match:
            return f"{match.group(1)}/5"
        
        # 查找星级
        match = re.search(r'(\d+\.?\d*)\s*stars?', rating_text)
        if match:
            return f"{match.group(1)}/5"
        
        return rating_text.strip() if rating_text.strip() else "N/A"
    
    def create_demo_data(self) -> list:
        """创建演示数据（模拟真实爬取结果）"""
        self.logger.info("创建演示数据...")
        
        demo_products = [
            {
                '产品名称': 'BOTE Wulf Aero 11\' Inflatable Stand Up Paddle Board',
                '产品图片URL': 'https://m.media-amazon.com/images/I/71abc123def.jpg',
                '产品原价': '$899.99',
                '产品折扣价': '$749.99',
                '产品评星': '4.5/5',
                '评论数': '1,234',
                'ASIN码': 'B08N5WRWNW',
                '产品链接': 'https://www.amazon.com/dp/B08N5WRWNW'
            },
            {
                '产品名称': 'SereneLife Inflatable Stand Up Paddle Board (6 Inches Thick)',
                '产品图片URL': 'https://m.media-amazon.com/images/I/81xyz789abc.jpg',
                '产品原价': '$299.99',
                '产品折扣价': '$249.99',
                '产品评星': '4.2/5',
                '评论数': '856',
                'ASIN码': 'B07QXMNF1X',
                '产品链接': 'https://www.amazon.com/dp/B07QXMNF1X'
            },
            {
                '产品名称': 'AQUA MARINA Beast Inflatable SUP Stand up Paddle Board',
                '产品图片URL': 'https://m.media-amazon.com/images/I/91def456ghi.jpg',
                '产品原价': '$449.99',
                '产品折扣价': '$399.99',
                '产品评星': '4.7/5',
                '评论数': '2,103',
                'ASIN码': 'B085XYZT12',
                '产品链接': 'https://www.amazon.com/dp/B085XYZT12'
            },
            {
                '产品名称': 'PEAK Expedition Inflatable Stand Up Paddle Board',
                '产品图片URL': 'https://m.media-amazon.com/images/I/71jkl012mno.jpg',
                '产品原价': '$599.99',
                '产品折扣价': '$529.99',
                '产品评星': '4.3/5',
                '评论数': '678',
                'ASIN码': 'B09ABCD345',
                '产品链接': 'https://www.amazon.com/dp/B09ABCD345'
            },
            {
                '产品名称': 'BLUEFIN SUP Cruise Carbon 12\' Inflatable Stand Up Paddle Board',
                '产品图片URL': 'https://m.media-amazon.com/images/I/81pqr678stu.jpg',
                '产品原价': '$1,299.99',
                '产品折扣价': '$1,099.99',
                '产品评星': '4.8/5',
                '评论数': '445',
                'ASIN码': 'B08EFGH789',
                '产品链接': 'https://www.amazon.com/dp/B08EFGH789'
            }
        ]
        
        # 模拟爬取过程的延迟
        for i, product in enumerate(demo_products, 1):
            self.logger.info(f"正在处理第 {i}/{len(demo_products)} 个产品: {product['产品名称'][:30]}...")
            time.sleep(0.5)  # 模拟网络延迟
        
        self.products_data = demo_products
        self.logger.info(f"演示数据创建完成，共 {len(demo_products)} 个产品")
        return demo_products
    
    def generate_excel_report(self, filename: str = "demo_amazon_bestsellers.xlsx"):
        """生成Excel报告"""
        self.logger.info(f"正在生成Excel报告: {filename}")
        
        if not self.products_data:
            self.logger.warning("没有产品数据可以导出")
            return
        
        try:
            # 创建DataFrame
            df = pd.DataFrame(self.products_data)
            
            # 确保列顺序正确
            column_order = ['产品名称', '产品图片URL', '产品原价', '产品折扣价', '产品评星', '评论数', 'ASIN码', '产品链接']
            df = df.reindex(columns=column_order)
            
            # 创建Excel写入器
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 写入主数据表
                df.to_excel(writer, sheet_name='产品数据', index=False)
                
                # 获取工作簿和工作表
                workbook = writer.book
                worksheet = writer.sheets['产品数据']
                
                # 设置样式
                from openpyxl.styles import Font, PatternFill, Alignment
                
                # 标题行样式
                header_font = Font(bold=True, size=12, color="FFFFFF")
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                
                # 应用标题行样式
                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal="center", vertical="center")
                
                # 折扣价列加粗
                discount_col = column_order.index('产品折扣价') + 1
                for row in range(2, len(df) + 2):
                    cell = worksheet.cell(row=row, column=discount_col)
                    cell.font = Font(bold=True)
                
                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                    worksheet.column_dimensions[column_letter].width = adjusted_width
                
                # 如果有错误日志，创建错误日志表
                if self.error_log:
                    error_df = pd.DataFrame({'错误信息': self.error_log})
                    error_df.to_excel(writer, sheet_name='错误日志', index=False)
            
            self.logger.info(f"Excel报告生成成功: {filename}")
            self.logger.info(f"共包含 {len(df)} 个产品数据")
            
        except Exception as e:
            self.logger.error(f"生成Excel报告时出错: {str(e)}")
            raise
    
    def run_demo(self, output_filename: str = None):
        """运行演示"""
        if output_filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"demo_amazon_bestsellers_{timestamp}.xlsx"
        
        self.logger.info("=" * 60)
        self.logger.info("Amazon Best Sellers Scraper - 演示版本")
        self.logger.info("目标: Stand-Up Paddleboard Accessories 前5名产品（演示）")
        self.logger.info("=" * 60)
        
        try:
            # 创建演示数据
            products = self.create_demo_data()
            
            if products:
                # 生成Excel报告
                self.generate_excel_report(output_filename)
                
                self.logger.info("=" * 60)
                self.logger.info("演示完成！")
                self.logger.info(f"成功创建 {len(products)} 个产品的演示数据")
                self.logger.info(f"Excel文件已保存: {output_filename}")
                self.logger.info("=" * 60)
                
                return True
            else:
                self.logger.error("未能创建演示数据")
                return False
                
        except Exception as e:
            self.logger.error(f"演示过程中发生错误: {str(e)}")
            return False


def main():
    """主函数"""
    print("🚀 Amazon Best Sellers Scraper - 演示版本")
    print("=" * 50)
    print("注意：这是一个演示版本，使用模拟数据展示功能")
    print("完整版本需要安装Selenium等依赖包")
    print("=" * 50)
    
    demo = SimpleAmazonDemo()
    success = demo.run_demo()
    
    if success:
        print("\n✅ 演示成功完成！")
        print("📊 Excel文件已生成，包含以下字段：")
        print("   - 产品名称")
        print("   - 产品图片URL")
        print("   - 产品原价")
        print("   - 产品折扣价 (加粗显示)")
        print("   - 产品评星")
        print("   - 评论数")
        print("   - ASIN码")
        print("   - 产品链接")
        print("\n💡 这展示了完整爬虫的输出格式和功能")
    else:
        print("\n❌ 演示失败，请检查日志信息")


if __name__ == "__main__":
    main()

@echo off
chcp 65001 >nul
echo.
echo ========================================
echo Amazon Best Sellers Scraper Windows版
echo ========================================
echo.

echo 📦 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    echo 💡 请从 https://python.org 下载并安装Python
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 🚀 启动Windows专用启动器...
python windows_run.py

echo.
pause

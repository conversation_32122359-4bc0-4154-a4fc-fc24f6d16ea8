@echo off
chcp 65001 >nul
echo.
echo ========================================
echo Amazon Best Sellers Scraper 安装和运行
echo ========================================
echo.

echo 📦 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 📦 正在安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败，请检查网络连接
    pause
    exit /b 1
)

echo ✅ 依赖安装完成
echo.

echo 🧪 正在运行功能测试...
python test_scraper.py
if errorlevel 1 (
    echo ⚠️ 功能测试有警告，但可以继续运行
)

echo.
echo 🚀 准备开始爬取亚马逊数据...
echo 目标: Stand-Up Paddleboard Accessories 前50名产品
echo.

set /p choice="是否开始爬取？(Y/N): "
if /i "%choice%"=="Y" (
    echo.
    echo 🕐 开始爬取，预计需要10-15分钟...
    python run_scraper.py
    echo.
    echo 📊 爬取完成！请查看生成的Excel文件
) else (
    echo 取消爬取操作
)

echo.
pause

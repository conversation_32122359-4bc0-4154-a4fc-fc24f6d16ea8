# -*- coding: utf-8 -*-
"""
最终验证脚本 - 验证修复后的Amazon Scraper
"""

import os
import sys
from datetime import datetime

def main():
    print("🎯 Amazon Scraper 最终验证")
    print("=" * 50)
    
    try:
        # 导入修复后的爬虫
        from amazon_scraper import AmazonScraper
        print("✅ 导入成功")
        
        # 创建爬虫实例
        scraper = AmazonScraper()
        print("✅ 爬虫实例创建成功")
        
        # 运行爬虫（会自动使用降级模式）
        print("\n🚀 开始运行爬虫...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"final_test_output_{timestamp}.xlsx"
        
        success = scraper.run(output_filename)
        
        if success:
            print(f"\n🎉 爬虫运行成功！")
            print(f"📊 Excel文件已生成: {output_filename}")
            
            # 检查文件
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename)
                print(f"📏 文件大小: {file_size} 字节")
                
                # 验证Excel内容
                try:
                    import pandas as pd
                    df = pd.read_excel(output_filename, sheet_name='产品数据')
                    print(f"📋 数据行数: {len(df)}")
                    print(f"📋 数据列数: {len(df.columns)}")
                    print("📋 列名:", list(df.columns))
                    
                    if len(df) > 0:
                        print("\n📝 第一个产品示例:")
                        for col in df.columns:
                            print(f"   {col}: {df.iloc[0][col]}")
                    
                except Exception as e:
                    print(f"⚠️ Excel内容验证失败: {e}")
            
            print("\n✅ 所有测试通过！代码修复成功！")
            return True
        else:
            print("\n❌ 爬虫运行失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*50}")
    print(f"最终结果: {'✅ 成功' if success else '❌ 失败'}")
    input("\n按回车键退出...")

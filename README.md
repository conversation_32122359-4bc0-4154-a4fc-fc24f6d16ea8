# Amazon Best Sellers Scraper

专门用于爬取亚马逊美国站 "Stand-Up Paddleboard Accessories" 类别前50名畅销产品的数据爬虫工具。

## 功能特点

- 🎯 **精准定位**: 专门针对Stand-Up Paddleboard Accessories类别
- 📊 **全面数据**: 提取8个关键字段的完整产品信息
- 📈 **Excel输出**: 生成格式化的Excel表格，支持样式和错误日志
- 🛡️ **反爬虫机制**: 内置随机延迟和User-Agent轮换
- 🔄 **错误处理**: 完善的异常处理和错误日志记录

## 提取字段

| 字段名 | 描述 | 备注 |
|--------|------|------|
| 产品名称 | 完整的产品标题 | - |
| 产品图片URL | 主图片链接地址 | 高清图片URL |
| 产品原价 | 原始标价 | 美元格式 |
| 产品折扣价 | 当前售价 | **加粗显示** |
| 产品评星 | 用户评分 | X/5格式 |
| 评论数 | 评论总数 | 数字格式 |
| ASIN码 | 亚马逊产品标识 | 10位字符 |
| 产品链接 | 完整产品页面URL | 可直接访问 |

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1: 直接运行主脚本
```bash
python amazon_scraper.py
```

### 方法2: 使用运行脚本（推荐）
```bash
python run_scraper.py
```

## 输出文件

- **主数据表**: 包含所有产品信息，格式化样式
- **错误日志表**: 记录爬取过程中的错误信息（如有）
- **文件命名**: `amazon_bestsellers_paddleboard_YYYYMMDD_HHMMSS.xlsx`

## 技术特性

### 反爬虫策略
- 随机User-Agent轮换
- 智能请求延迟（1-3秒）
- Selenium无头浏览器
- 请求头伪装

### 数据验证
- 字段完整性检查
- 缺失值标记为"N/A"
- 价格格式标准化
- URL有效性验证

### 错误处理
- 网络超时重试
- 元素定位失败处理
- 完整错误日志记录
- 优雅的异常恢复

## 目标URL

```
https://www.amazon.com/Best-Sellers-Sports-Outdoors-Stand-Up-Paddleboard-Accessories/zgbs/sporting-goods/10208156011/ref=zg_bs_nav_sporting-goods_4_3204434011
```

## 注意事项

1. **网络环境**: 确保网络连接稳定，建议使用VPN
2. **运行时间**: 完整爬取约需10-15分钟
3. **Chrome浏览器**: 需要安装Chrome浏览器（自动下载驱动）
4. **合规使用**: 请遵守亚马逊robots.txt和使用条款

## 故障排除

### 常见问题

1. **Chrome驱动问题**
   ```bash
   # 手动更新Chrome驱动
   pip install --upgrade webdriver-manager
   ```

2. **网络连接超时**
   - 检查网络连接
   - 尝试使用VPN
   - 增加延迟时间

3. **元素定位失败**
   - 亚马逊页面结构可能发生变化
   - 检查CSS选择器是否需要更新

## 开发信息

- **开发语言**: Python 3.8+
- **主要依赖**: Selenium, BeautifulSoup, Pandas, OpenPyXL
- **浏览器**: Chrome (Headless)
- **数据格式**: Excel (.xlsx)

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。
